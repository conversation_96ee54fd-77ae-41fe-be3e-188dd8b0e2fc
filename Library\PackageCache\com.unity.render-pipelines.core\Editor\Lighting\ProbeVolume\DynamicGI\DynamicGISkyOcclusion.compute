#pragma only_renderers d3d11 playstation xboxone xboxseries vulkan metal

#define UNIFIED_RT_BACKEND_COMPUTE
#define UNIFIED_RT_GROUP_SIZE_X 64
#define UNIFIED_RT_GROUP_SIZE_Y 1
#include "DynamicGISkyOcclusion.hlsl"
#include_with_pragmas "Packages/com.unity.rendering.light-transport/Runtime/UnifiedRayTracing/Compute/ComputeRaygenShader.hlsl"

#pragma kernel EncodeShadingDirection

StructuredBuffer<float3> _SkyShadingPrecomputedDirection;
StructuredBuffer<float3> _SkyShadingDirections;
RWStructuredBuffer<uint> _SkyShadingIndices;

uint _ProbeCount;

uint LinearSearchClosestDirection(float3 direction)
{
    int indexMax = 255;
    float bestDot = -10.0f;
    int bestIndex = 0;

    for (int index=0; index< indexMax; index++)
    {
        float currentDot = dot(direction, _SkyShadingPrecomputedDirection[index]);
        if (currentDot > bestDot)
        {
            bestDot = currentDot;
            bestIndex = index;
        }
    }
    return bestIndex;
}

[numthreads(64, 1, 1)]
void EncodeShadingDirection(uint probeId : SV_DispatchThreadID)
{
    if (probeId >= _ProbeCount)
        return;

    uint bestDirectionIndex = 255;
    float norm = length(_SkyShadingDirections[probeId]);
    if (norm > 0.0001f)
        bestDirectionIndex = LinearSearchClosestDirection(_SkyShadingDirections[probeId] / norm);

    _SkyShadingIndices[probeId] = bestDirectionIndex;
}
